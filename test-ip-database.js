// Test the new IP database
const { testIP, lookupIPInDatabase } = require('./backend/utils/ipDatabase');

console.log('🧪 Testing IP Database\n');

const testIPs = [
  '**************', // Your specific IP
  '**************', // From console logs
  '***********',    // Tunisia range
  '**********',     // Tunisia North Africa
  '***********',    // France
  '*******',        // US
  '127.0.0.1',      // Local
  '999.999.999.999' // Invalid
];

testIPs.forEach(ip => {
  const result = testIP(ip);
  console.log(`${result.found ? '✅' : '❌'} ${ip} → ${result.result ? `${result.result.country} ${result.result.flag}` : 'Not found'}`);
});

console.log('\n🎯 Key Test: Your IP');
const yourIP = lookupIPInDatabase('**************');
if (yourIP) {
  console.log(`✅ SUCCESS: ************** → ${yourIP.country} ${yourIP.flag}`);
  console.log(`   Details: ${yourIP.city}, ${yourIP.region}`);
  console.log(`   Source: ${yourIP.source}`);
} else {
  console.log('❌ FAILED: ************** not found in database');
}
