// Quick test to verify IP detection logic
const testIP = '**************';

console.log('Testing IP:', testIP);

// Test the exact logic from backend
if (testIP.startsWith('102.') && (
    testIP.startsWith('102.169.') || testIP.startsWith('102.24.') || testIP.startsWith('102.172.') ||
    testIP.startsWith('102.157.') || testIP.startsWith('102.158.') || testIP.startsWith('102.159.') ||
    testIP.startsWith('102.160.') || testIP.startsWith('102.161.') || testIP.startsWith('102.162.') ||
    testIP.startsWith('102.163.') || testIP.startsWith('102.164.') || testIP.startsWith('102.165.') ||
    testIP.startsWith('102.166.') || testIP.startsWith('102.167.') || testIP.startsWith('102.168.') ||
    testIP.startsWith('102.170.') || testIP.startsWith('102.171.') || testIP.startsWith('102.173.') ||
    testIP.startsWith('102.174.') || testIP.startsWith('102.175.') || testIP.startsWith('102.176.') ||
    testIP.startsWith('102.177.') || testIP.startsWith('102.178.') || testIP.startsWith('102.179.') ||
    testIP.startsWith('102.180.') || testIP.startsWith('102.181.') || testIP.startsWith('102.182.') ||
    testIP.startsWith('102.183.') || testIP.startsWith('102.184.') || testIP.startsWith('102.185.') ||
    testIP.startsWith('102.186.') || testIP.startsWith('102.187.') || testIP.startsWith('102.188.') ||
    testIP.startsWith('102.189.') || testIP.startsWith('102.190.') || testIP.startsWith('102.191.') ||
    testIP.startsWith('102.192.') || testIP.startsWith('102.193.') || testIP.startsWith('102.194.') ||
    testIP.startsWith('102.195.') || testIP.startsWith('102.196.') || testIP.startsWith('102.197.') ||
    testIP.startsWith('102.198.') || testIP.startsWith('102.199.') || testIP.startsWith('102.200.') ||
    testIP.startsWith('102.140.') || testIP.startsWith('102.141.') || testIP.startsWith('102.142.') ||
    testIP.startsWith('102.143.') || testIP.startsWith('102.144.') || testIP.startsWith('102.145.') ||
    testIP.startsWith('102.146.') || testIP.startsWith('102.147.') || testIP.startsWith('102.148.') ||
    testIP.startsWith('102.149.') || testIP.startsWith('102.150.') || testIP.startsWith('102.151.') ||
    testIP.startsWith('102.152.') || testIP.startsWith('102.153.') || testIP.startsWith('102.154.') ||
    testIP.startsWith('102.155.') || testIP.startsWith('102.156.')
)) {
    console.log('✅ IP should be detected as Tunisia');
} else {
    console.log('❌ IP will NOT be detected - needs to be added');
}

// Test individual conditions
console.log('testIP.startsWith("102."):', testIP.startsWith('102.'));
console.log('testIP.startsWith("102.157."):', testIP.startsWith('102.157.'));
