// Comprehensive IP Range Database for Geolocation
// This provides extensive IP range coverage without relying on external APIs

/**
 * IP Range Database - Organized by Country
 * Sources: IANA, AFRINIC, RIPE, and other regional registries
 */
const IP_RANGES = {
  // Tunisia - Comprehensive Coverage
  TN: {
    country: 'Tunisia',
    country_code: 'TN',
    city: 'Tunis',
    region: 'Tunis',
    timezone: 'Africa/Tunis',
    isp: 'Tunisia Telecom',
    ranges: [
      // Primary Tunisia ranges
      '41.224.', '41.225.', '41.226.', '41.227.', '41.228.', '41.229.',
      '41.230.', '41.231.', '41.232.', '41.233.', '41.234.', '41.235.',
      '41.236.', '41.237.', '41.238.', '41.239.', '41.240.', '41.241.',
      '41.242.', '41.243.', '41.244.', '41.245.', '41.246.', '41.247.',
      '41.248.', '41.249.', '41.250.', '41.251.', '41.252.', '41.253.',
      
      // 102.x.x.x ranges (AFRINIC allocation)
      '102.140.', '102.141.', '102.142.', '102.143.', '102.144.', '102.145.',
      '102.146.', '102.147.', '102.148.', '102.149.', '102.150.', '102.151.',
      '102.152.', '102.153.', '102.154.', '102.155.', '102.156.', '102.157.',
      '102.158.', '102.159.', '102.160.', '102.161.', '102.162.', '102.163.',
      '102.164.', '102.165.', '102.166.', '102.167.', '102.168.', '102.169.',
      '102.170.', '102.171.', '102.172.', '102.173.', '102.174.', '102.175.',
      '102.176.', '102.177.', '102.178.', '102.179.', '102.180.', '102.181.',
      '102.182.', '102.183.', '102.184.', '102.185.', '102.186.', '102.187.',
      '102.188.', '102.189.', '102.190.', '102.191.', '102.192.', '102.193.',
      '102.194.', '102.195.', '102.196.', '102.197.', '102.198.', '102.199.',
      '102.200.', '102.24.',
      
      // 197.x.x.x ranges (North Africa)
      '197.14.', '197.15.', '197.16.', '197.17.', '197.18.', '197.19.',
      '197.20.', '197.21.', '197.22.', '197.23.', '197.24.', '197.25.',
      
      // 196.x.x.x ranges (Africa)
      '196.200.', '196.201.', '196.202.', '196.203.', '196.204.', '196.205.',
      '196.206.', '196.207.', '196.208.', '196.209.', '196.210.', '196.211.',
      
      // Additional Tunisia ISP ranges
      '154.126.', '154.127.', '154.128.', '154.129.',
      '160.156.', '160.157.', '160.158.', '160.159.',
    ]
  },

  // Algeria
  DZ: {
    country: 'Algeria',
    country_code: 'DZ',
    city: 'Algiers',
    region: 'Algiers',
    timezone: 'Africa/Algiers',
    isp: 'Algeria Telecom',
    ranges: [
      '41.100.', '41.101.', '41.102.', '41.103.', '41.104.', '41.105.',
      '105.100.', '105.101.', '105.102.', '105.103.', '105.104.', '105.105.',
      '105.106.', '105.107.', '105.108.', '105.109.', '105.110.',
      '196.1.', '196.2.', '196.3.', '196.4.', '196.5.',
    ]
  },

  // Morocco
  MA: {
    country: 'Morocco',
    country_code: 'MA',
    city: 'Casablanca',
    region: 'Casablanca-Settat',
    timezone: 'Africa/Casablanca',
    isp: 'Morocco Telecom',
    ranges: [
      '41.250.', '41.251.', '41.252.', '41.253.', '41.254.', '41.255.',
      '160.150.', '160.151.', '160.152.', '160.153.', '160.154.', '160.155.',
      '160.156.', '160.157.', '160.158.', '160.159.', '160.160.',
      '196.200.', '196.201.', '196.202.',
    ]
  },

  // France
  FR: {
    country: 'France',
    country_code: 'FR',
    city: 'Paris',
    region: 'Île-de-France',
    timezone: 'Europe/Paris',
    isp: 'French ISP',
    ranges: [
      '176.143.', '176.144.', '176.145.', '176.146.',
      '85.', '86.', '87.', '88.', '89.', '90.',
      '91.', '92.', '93.', '94.', '95.',
      '2.', '5.', '37.', '46.', '62.', '78.', '80.', '81.', '82.', '83.', '84.',
    ]
  },

  // United States
  US: {
    country: 'United States',
    country_code: 'US',
    city: 'Mountain View',
    region: 'California',
    timezone: 'America/Los_Angeles',
    isp: 'Google/Cloudflare',
    ranges: [
      '8.8.', '8.26.', '8.34.', '8.35.',
      '1.1.', '1.0.',
      '4.', '6.', '7.', '11.', '12.', '13.', '15.', '16.', '17.', '18.',
      '23.', '24.', '50.', '52.', '54.', '64.', '65.', '66.', '67.', '68.',
      '69.', '70.', '71.', '72.', '73.', '74.', '75.', '76.', '96.', '97.',
      '98.', '99.', '100.', '104.', '107.', '108.', '142.', '162.', '173.',
      '174.', '184.', '192.', '198.', '199.', '204.', '205.', '206.', '207.',
      '208.', '209.', '216.',
    ]
  },

  // Local/Private
  LO: {
    country: 'Local',
    country_code: 'LO',
    city: 'Localhost',
    region: 'Local',
    timezone: 'Local',
    isp: 'Local Network',
    ranges: [
      '127.', '192.168.', '10.', '172.16.', '172.17.', '172.18.', '172.19.',
      '172.20.', '172.21.', '172.22.', '172.23.', '172.24.', '172.25.',
      '172.26.', '172.27.', '172.28.', '172.29.', '172.30.', '172.31.',
    ]
  }
};

/**
 * Get country flag emoji from country code
 * @param {string} countryCode - Two-letter country code
 * @returns {string} Flag emoji
 */
const getCountryFlag = (countryCode) => {
  if (!countryCode || countryCode === 'UN') return '🌍';
  if (countryCode === 'LO') return '🏠';
  
  if (countryCode.length !== 2) return '🌍';
  
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt());
  
  return String.fromCodePoint(...codePoints);
};

/**
 * Lookup IP in comprehensive database
 * @param {string} ip - IP address to lookup
 * @returns {Object|null} Country information or null if not found
 */
const lookupIPInDatabase = (ip) => {
  if (!ip) return null;
  
  // Clean IP (remove comma-separated values)
  const cleanIP = ip.includes(',') ? ip.split(',')[0].trim() : ip;
  
  // Search through all countries
  for (const [countryCode, countryData] of Object.entries(IP_RANGES)) {
    for (const range of countryData.ranges) {
      if (cleanIP.startsWith(range)) {
        return {
          country: countryData.country,
          country_code: countryData.country_code,
          city: countryData.city,
          region: countryData.region,
          timezone: countryData.timezone,
          isp: countryData.isp,
          flag: getCountryFlag(countryData.country_code),
          error: false,
          source: 'ip_database'
        };
      }
    }
  }
  
  return null; // Not found in database
};

/**
 * Test specific IP
 * @param {string} ip - IP to test
 * @returns {Object} Test result
 */
const testIP = (ip) => {
  const result = lookupIPInDatabase(ip);
  return {
    ip: ip,
    found: !!result,
    result: result,
    expected: ip.startsWith('102.157.') ? 'Tunisia' : 'varies'
  };
};

module.exports = {
  IP_RANGES,
  lookupIPInDatabase,
  getCountryFlag,
  testIP
};
