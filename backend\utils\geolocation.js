// Backend IP Geolocation utility service
// Server-side implementation to avoid CORS issues

const https = require('https');
const { lookupIPInDatabase } = require('./ipDatabase');

const GEOLOCATION_CACHE = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const RATE_LIMIT_DELAY = 5000; // 5 seconds between requests (more conservative)
const MAX_RETRIES = 2;
let lastRequestTime = 0;

/**
 * Get country information using IP heuristics (faster and more reliable)
 * @param {string} ip - IP address to analyze
 * @returns {Object} Country information object
 */
const getCountryFromIPHeuristics = (ip) => {
  // Enhanced IP geolocation heuristics for common ranges

  // Tunisia IP ranges (most common for your visitors) - EXPANDED
  if (ip.startsWith('41.227.') || ip.startsWith('41.230.') || ip.startsWith('41.225.') ||
      ip.startsWith('41.224.') || ip.startsWith('41.226.') || ip.startsWith('41.228.') ||
      ip.startsWith('41.229.') || ip.startsWith('41.231.') || ip.startsWith('41.232.') ||
      ip.startsWith('41.233.') || ip.startsWith('41.234.') || ip.startsWith('41.235.') ||
      ip.startsWith('41.236.') || ip.startsWith('41.237.') || ip.startsWith('41.238.') ||
      ip.startsWith('41.239.') || ip.startsWith('41.240.') || ip.startsWith('41.241.') ||
      ip.startsWith('41.242.') || ip.startsWith('41.243.') || ip.startsWith('41.244.') ||
      ip.startsWith('41.245.') || ip.startsWith('41.246.') || ip.startsWith('41.247.') ||
      ip.startsWith('41.248.') || ip.startsWith('41.249.')) {
    return {
      country: 'Tunisia',
      country_code: 'TN',
      city: 'Tunis',
      region: 'Tunis',
      timezone: 'Africa/Tunis',
      isp: 'Tunisia Telecom',
      flag: getCountryFlag('TN'),
      error: false
    };
  }

  // More Tunisia IP ranges - MASSIVELY EXPANDED COVERAGE including 102.157.x.x
  if (ip.startsWith('102.') && (
      // Original ranges
      ip.startsWith('102.169.') || ip.startsWith('102.24.') || ip.startsWith('102.172.') ||
      // Expanded 102.157.x.x range (covers ************** and similar)
      ip.startsWith('102.157.') || ip.startsWith('102.158.') || ip.startsWith('102.159.') ||
      ip.startsWith('102.160.') || ip.startsWith('102.161.') || ip.startsWith('102.162.') ||
      ip.startsWith('102.163.') || ip.startsWith('102.164.') || ip.startsWith('102.165.') ||
      ip.startsWith('102.166.') || ip.startsWith('102.167.') || ip.startsWith('102.168.') ||
      ip.startsWith('102.170.') || ip.startsWith('102.171.') || ip.startsWith('102.173.') ||
      ip.startsWith('102.174.') || ip.startsWith('102.175.') || ip.startsWith('102.176.') ||
      ip.startsWith('102.177.') || ip.startsWith('102.178.') || ip.startsWith('102.179.') ||
      ip.startsWith('102.180.') || ip.startsWith('102.181.') || ip.startsWith('102.182.') ||
      ip.startsWith('102.183.') || ip.startsWith('102.184.') || ip.startsWith('102.185.') ||
      ip.startsWith('102.186.') || ip.startsWith('102.187.') || ip.startsWith('102.188.') ||
      ip.startsWith('102.189.') || ip.startsWith('102.190.') || ip.startsWith('102.191.') ||
      ip.startsWith('102.192.') || ip.startsWith('102.193.') || ip.startsWith('102.194.') ||
      ip.startsWith('102.195.') || ip.startsWith('102.196.') || ip.startsWith('102.197.') ||
      ip.startsWith('102.198.') || ip.startsWith('102.199.') || ip.startsWith('102.200.') ||
      // Additional Tunisia ranges
      ip.startsWith('102.140.') || ip.startsWith('102.141.') || ip.startsWith('102.142.') ||
      ip.startsWith('102.143.') || ip.startsWith('102.144.') || ip.startsWith('102.145.') ||
      ip.startsWith('102.146.') || ip.startsWith('102.147.') || ip.startsWith('102.148.') ||
      ip.startsWith('102.149.') || ip.startsWith('102.150.') || ip.startsWith('102.151.') ||
      ip.startsWith('102.152.') || ip.startsWith('102.153.') || ip.startsWith('102.154.') ||
      ip.startsWith('102.155.') || ip.startsWith('102.156.')
  )) {
    return {
      country: 'Tunisia',
      country_code: 'TN',
      city: 'Tunis',
      region: 'Tunis',
      timezone: 'Africa/Tunis',
      isp: 'Tunisia ISP',
      flag: getCountryFlag('TN'),
      error: false
    };
  }

  // Tunisia - North Africa range - EXPANDED
  if (ip.startsWith('197.14.') || ip.startsWith('197.15.') || ip.startsWith('197.16.') ||
      ip.startsWith('197.17.') || ip.startsWith('197.18.') || ip.startsWith('197.19.') ||
      ip.startsWith('197.20.') || ip.startsWith('197.21.') || ip.startsWith('197.22.') ||
      ip.startsWith('197.23.') || ip.startsWith('197.24.') || ip.startsWith('197.25.')) {
    return {
      country: 'Tunisia',
      country_code: 'TN',
      city: 'Tunis',
      region: 'Tunis',
      timezone: 'Africa/Tunis',
      isp: 'Tunisia Network',
      flag: getCountryFlag('TN'),
      error: false
    };
  }

  // General Africa ranges (196.x.x.x) - could be various African countries
  if (ip.startsWith('196.200.') || ip.startsWith('196.201.') || ip.startsWith('196.202.') ||
      ip.startsWith('196.203.') || ip.startsWith('196.204.') || ip.startsWith('196.205.') ||
      ip.startsWith('196.206.') || ip.startsWith('196.207.') || ip.startsWith('196.208.') ||
      ip.startsWith('196.209.') || ip.startsWith('196.210.') || ip.startsWith('196.211.')) {
    return {
      country: 'Tunisia',
      country_code: 'TN',
      city: 'Tunis',
      region: 'Tunis',
      timezone: 'Africa/Tunis',
      isp: 'Tunisia/North Africa ISP',
      flag: getCountryFlag('TN'),
      error: false
    };
  }

  // France/Europe
  if (ip.startsWith('176.143.') || ip.startsWith('176.144.') || ip.startsWith('85.')) {
    return {
      country: 'France',
      country_code: 'FR',
      city: 'Paris',
      region: 'Île-de-France',
      timezone: 'Europe/Paris',
      isp: 'French ISP',
      flag: getCountryFlag('FR'),
      error: false
    };
  }

  // United States - Google/Cloudflare DNS
  if (ip.startsWith('8.8.') || ip.startsWith('1.1.')) {
    return {
      country: 'United States',
      country_code: 'US',
      city: 'Mountain View',
      region: 'California',
      timezone: 'America/Los_Angeles',
      isp: 'Google/Cloudflare',
      flag: getCountryFlag('US'),
      error: false
    };
  }

  // Algeria - EXPANDED
  if (ip.startsWith('41.100.') || ip.startsWith('41.101.') || ip.startsWith('41.102.') ||
      ip.startsWith('41.103.') || ip.startsWith('41.104.') || ip.startsWith('41.105.') ||
      ip.startsWith('105.100.') || ip.startsWith('105.101.') || ip.startsWith('105.102.') ||
      ip.startsWith('105.103.') || ip.startsWith('105.104.') || ip.startsWith('105.105.') ||
      ip.startsWith('105.106.') || ip.startsWith('105.107.') || ip.startsWith('105.108.') ||
      ip.startsWith('105.109.') || ip.startsWith('105.110.')) {
    return {
      country: 'Algeria',
      country_code: 'DZ',
      city: 'Algiers',
      region: 'Algiers',
      timezone: 'Africa/Algiers',
      isp: 'Algeria Telecom',
      flag: getCountryFlag('DZ'),
      error: false
    };
  }

  // Morocco - EXPANDED
  if (ip.startsWith('41.250.') || ip.startsWith('41.251.') || ip.startsWith('41.252.') ||
      ip.startsWith('41.253.') || ip.startsWith('41.254.') || ip.startsWith('41.255.') ||
      ip.startsWith('160.150.') || ip.startsWith('160.151.') || ip.startsWith('160.152.') ||
      ip.startsWith('160.153.') || ip.startsWith('160.154.') || ip.startsWith('160.155.') ||
      ip.startsWith('160.156.') || ip.startsWith('160.157.') || ip.startsWith('160.158.') ||
      ip.startsWith('160.159.') || ip.startsWith('160.160.')) {
    return {
      country: 'Morocco',
      country_code: 'MA',
      city: 'Casablanca',
      region: 'Casablanca-Settat',
      timezone: 'Africa/Casablanca',
      isp: 'Morocco Telecom',
      flag: getCountryFlag('MA'),
      error: false
    };
  }

  // Default fallback
  return {
    country: 'Unknown',
    country_code: 'UN',
    city: 'Unknown',
    region: 'Unknown',
    timezone: 'Unknown',
    isp: 'Unknown',
    flag: '🌍',
    error: true,
    errorMessage: 'IP range not recognized'
  };
};

/**
 * Get country information for an IP address (Backend version)
 * @param {string} ip - IP address to lookup
 * @returns {Promise<Object>} Country information object
 */
const getCountryFromIP = async (ip) => {
  // Clean IP address - remove any comma-separated values
  if (ip && ip.includes(',')) {
    ip = ip.split(',')[0].trim();
  }

  // Check if IP is localhost or private
  if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      country_code: 'LO',
      city: 'Localhost',
      region: 'Local',
      flag: '🏠',
      error: false
    };
  }

  // Check cache first
  const cacheKey = ip;
  const cached = GEOLOCATION_CACHE.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    console.log(`Using cached data for IP: ${ip}`);
    return cached.data;
  }

  // Try comprehensive IP database first (most reliable)
  const databaseResult = lookupIPInDatabase(ip);
  if (databaseResult) {
    console.log(`✅ Database match for IP: ${ip} → ${databaseResult.country} ${databaseResult.flag}`);

    // Cache the database result
    GEOLOCATION_CACHE.set(cacheKey, {
      data: databaseResult,
      timestamp: Date.now()
    });

    return databaseResult;
  }

  // Fallback to legacy heuristics
  const heuristicResult = getCountryFromIPHeuristics(ip);
  if (heuristicResult.country !== 'Unknown') {
    console.log(`Using legacy heuristic data for IP: ${ip} → ${heuristicResult.country}`);

    // Cache the heuristic result
    GEOLOCATION_CACHE.set(cacheKey, {
      data: heuristicResult,
      timestamp: Date.now()
    });

    return heuristicResult;
  }

  // Try multiple free geolocation APIs with rate limiting protection
  console.log(`No heuristic match for IP: ${ip}, trying external APIs with rate limiting...`);

  // Check rate limiting
  const now = Date.now();
  if (now - lastRequestTime < RATE_LIMIT_DELAY) {
    console.log(`Rate limit protection: skipping API call for ${ip}`);
    const fallbackData = {
      country: 'Unknown',
      country_code: 'UN',
      city: 'Unknown',
      region: 'Unknown',
      timezone: 'Unknown',
      isp: 'Unknown',
      flag: '🌍',
      error: true,
      errorMessage: 'Rate limit protection - using fallback data'
    };

    GEOLOCATION_CACHE.set(cacheKey, {
      data: fallbackData,
      timestamp: Date.now()
    });

    return fallbackData;
  }

  // Try external APIs with multiple fallbacks
  try {
    lastRequestTime = now;
    const apiResult = await tryMultipleGeolocationAPIs(ip);

    // Cache successful result
    GEOLOCATION_CACHE.set(cacheKey, {
      data: apiResult,
      timestamp: Date.now()
    });

    return apiResult;
  } catch (apiError) {
    console.log(`All APIs failed for ${ip}:`, apiError.message);

    const fallbackData = {
      country: 'Unknown',
      country_code: 'UN',
      city: 'Unknown',
      region: 'Unknown',
      timezone: 'Unknown',
      isp: 'Unknown',
      flag: '🌍',
      error: true,
      errorMessage: `API lookup failed: ${apiError.message}`
    };

    GEOLOCATION_CACHE.set(cacheKey, {
      data: fallbackData,
      timestamp: Date.now()
    });

    return fallbackData;
  }
};

/**
 * Try multiple geolocation APIs with fallbacks
 * @param {string} ip - IP address to lookup
 * @returns {Promise<Object>} Geolocation data
 */
const tryMultipleGeolocationAPIs = async (ip) => {
  const apis = [
    // API 1: ip-api.com (free, 1000 requests/month)
    {
      name: 'ip-api.com',
      makeRequest: () => makeGeolocationRequest(ip, {
        hostname: 'ip-api.com',
        path: `/json/${ip}?fields=status,message,country,countryCode,region,city,timezone,isp`,
        timeout: 8000
      }),
      parseResponse: (data) => {
        if (data.status === 'fail') {
          throw new Error(data.message || 'API returned fail status');
        }
        return {
          country: data.country || 'Unknown',
          country_code: data.countryCode || 'UN',
          city: data.city || 'Unknown',
          region: data.region || 'Unknown',
          timezone: data.timezone || 'Unknown',
          isp: data.isp || 'Unknown',
          flag: getCountryFlag(data.countryCode),
          error: false
        };
      }
    },
    // API 2: ipapi.co (backup)
    {
      name: 'ipapi.co',
      makeRequest: () => makeGeolocationRequest(ip, {
        hostname: 'ipapi.co',
        path: `/${ip}/json/`,
        timeout: 8000
      }),
      parseResponse: (data) => {
        if (data.error) {
          throw new Error(data.reason || 'API error');
        }
        return {
          country: data.country_name || 'Unknown',
          country_code: data.country_code || 'UN',
          city: data.city || 'Unknown',
          region: data.region || 'Unknown',
          timezone: data.timezone || 'Unknown',
          isp: data.org || 'Unknown',
          flag: getCountryFlag(data.country_code),
          error: false
        };
      }
    }
  ];

  let lastError;

  for (const api of apis) {
    try {
      console.log(`Trying ${api.name} for IP: ${ip}`);
      const rawData = await api.makeRequest();
      const parsedData = api.parseResponse(rawData);
      console.log(`✅ ${api.name} success for ${ip}: ${parsedData.country}`);
      return parsedData;
    } catch (error) {
      console.log(`❌ ${api.name} failed for ${ip}: ${error.message}`);
      lastError = error;
      // Continue to next API
    }
  }

  // All APIs failed
  throw lastError || new Error('All geolocation APIs failed');
};

/**
 * Make HTTP request to geolocation API
 * @param {string} ip - IP address to lookup
 * @param {Object} options - Request options
 * @returns {Promise<Object>} API response data
 */
const makeGeolocationRequest = (ip, requestOptions = {}) => {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      hostname: 'ipapi.co',
      path: `/${ip}/json/`,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Portfolio-Backend/1.0'
      },
      timeout: 10000
    };

    const options = { ...defaultOptions, ...requestOptions };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          // Check for rate limiting first
          if (res.statusCode === 429) {
            reject(new Error('Too many requests - rate limited'));
            return;
          }

          // Check if response looks like HTML (rate limit page)
          if (data.trim().startsWith('<') || data.includes('Too many requests')) {
            reject(new Error('Too many requests - rate limited (HTML response)'));
            return;
          }

          const jsonData = JSON.parse(data);

          if (res.statusCode !== 200) {
            reject(new Error(`HTTP ${res.statusCode}: ${jsonData.reason || 'API error'}`));
            return;
          }

          if (jsonData.error) {
            reject(new Error(jsonData.reason || 'API error'));
            return;
          }

          resolve(jsonData);
        } catch (parseError) {
          // If JSON parsing fails, check if it's a rate limit response
          if (data.includes('Too many requests') || data.includes('rate limit')) {
            reject(new Error('Too many requests - rate limited'));
          } else {
            reject(new Error(`JSON parse error: ${parseError.message}`));
          }
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request error: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
};

/**
 * Get country flag emoji from country code
 * @param {string} countryCode - Two-letter country code
 * @returns {string} Flag emoji
 */
const getCountryFlag = (countryCode) => {
  if (!countryCode || countryCode === 'UN' || countryCode === 'LO') {
    return '🌍';
  }

  // Hardcoded flag mapping for reliability (no API needed!)
  const flagMap = {
    'TN': '🇹🇳', // Tunisia
    'FR': '🇫🇷', // France
    'US': '🇺🇸', // United States
    'DZ': '🇩🇿', // Algeria
    'MA': '🇲🇦', // Morocco
    'DE': '🇩🇪', // Germany
    'GB': '🇬🇧', // United Kingdom
    'IT': '🇮🇹', // Italy
    'ES': '🇪🇸', // Spain
    'CA': '🇨🇦', // Canada
    'JP': '🇯🇵', // Japan
    'AU': '🇦🇺', // Australia
    'BR': '🇧🇷', // Brazil
    'IN': '🇮🇳', // India
    'CN': '🇨🇳', // China
    'RU': '🇷🇺', // Russia
    'EG': '🇪🇬', // Egypt
    'SA': '🇸🇦', // Saudi Arabia
    'AE': '🇦🇪', // UAE
    'TR': '🇹🇷', // Turkey
    'LY': '🇱🇾', // Libya
    'LB': '🇱🇧', // Lebanon
    'SY': '🇸🇾', // Syria
    'JO': '🇯🇴', // Jordan
    'IQ': '🇮🇶', // Iraq
    'IR': '🇮🇷', // Iran
    'IL': '🇮🇱', // Israel
    'PS': '🇵🇸', // Palestine
    'LO': '🏠'  // Local
  };

  const upperCode = countryCode.toUpperCase();

  // First try: use hardcoded mapping
  if (flagMap[upperCode]) {
    return flagMap[upperCode];
  }

  // Second try: generate from Unicode (fallback)
  try {
    const codePoints = upperCode
      .split('')
      .map(char => 127397 + char.charCodeAt());

    return String.fromCodePoint(...codePoints);
  } catch (error) {
    console.warn(`Failed to generate flag for ${countryCode}:`, error.message);
    return '🌍';
  }
};

/**
 * Batch process multiple IPs with rate limiting
 * @param {string[]} ips - Array of IP addresses
 * @returns {Promise<Object>} Object with IP as key and geo data as value
 */
const batchGetCountriesFromIPs = async (ips) => {
  console.log(`Starting batch geolocation lookup for ${ips.length} IPs`);
  const results = {};
  
  // Remove duplicates and clean IPs
  const uniqueIPs = [...new Set(ips.map(ip => {
    if (ip && ip.includes(',')) {
      return ip.split(',')[0].trim();
    }
    return ip;
  }))].filter(ip => ip && ip.trim() !== '');

  console.log(`Processing ${uniqueIPs.length} unique IPs`);

  for (let i = 0; i < uniqueIPs.length; i++) {
    const ip = uniqueIPs[i];
    console.log(`Processing IP ${i + 1}/${uniqueIPs.length}: ${ip}`);

    try {
      const countryInfo = await getCountryFromIP(ip);
      results[ip] = countryInfo;
    } catch (error) {
      console.warn(`Failed to get geolocation for IP ${ip}:`, error);
      results[ip] = {
        country: 'Unknown',
        country_code: 'UN',
        city: 'Unknown',
        region: 'Unknown',
        timezone: 'Unknown',
        isp: 'Unknown',
        flag: '🌍',
        error: true,
        errorMessage: error.message
      };
    }
  }

  console.log('Batch geolocation lookup completed');
  return results;
};

module.exports = {
  getCountryFromIP,
  batchGetCountriesFromIPs,
  getCountryFlag
};
